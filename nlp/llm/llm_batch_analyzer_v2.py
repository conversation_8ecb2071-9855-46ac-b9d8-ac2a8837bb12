import argparse
import asyncio
from datetime import date, datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Set
from contextlib import asynccontextmanager
from dataclasses import dataclass

from apis.llm.anthropic import AnthropicManager
from apis.llm.base import BaseAPIManager
from apis.llm.data_types import (
    ACTIVE_BATCH_STATUSES,
    COMPLETED_BATCH_STATUSES,
    INCOMPLETED_BATCH_STATUSES,
    BatchStatus,
    CompletionRequest,
    CompletionStatus
)
from apis.llm.openai import OpenAIManager
from apis.yahoo_finance import yahoo_api

from db.database import DatabaseManager
from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import (
    TZ,
    LLMConfig,
    extract_article_id,
    generate_custom_id,
    process_article
)

# Configure logging
from utils.logging_config import configure_logging
logger = configure_logging(__name__, log_file='llm_analyzer_v2.log')


@dataclass
class APIConfig:
    """Configuration for a specific API processor."""
    # Batch processing
    max_concurrent_batches: int = 50

    # Retry and backoff configuration
    max_retries: int = 2
    initial_backoff_delay: float = 1.0  # Initial delay in seconds
    max_backoff_delay: float = 300.0  # Maximum delay in seconds
    backoff_multiplier: float = 2.0  # Exponential backoff multiplier

    # Priority
    priority_level: int = 1  # Lower number = higher priority

    # Timeout configuration
    api_request_timeout: float = 30.0  # Timeout for individual API requests
    batch_creation_timeout: float = 120.0  # Timeout for batch creation

    # Queue access permissions
    can_access_high_priority: bool = True
    can_access_low_priority: bool = True
    can_access_retry_queue: bool = True


@dataclass
class AsyncBatchConfig:
    """Configuration for async batch processing v2."""
    # Global batch processing
    max_articles_per_cycle: int = 1000

    # Queue configuration
    high_priority_queue_maxsize: int = 50
    low_priority_queue_maxsize: int = 100
    retry_queue_maxsize: int = 30

    # Polling intervals (seconds)
    batch_creation_interval: int = 60  # How often to check for new articles
    batch_monitoring_interval: int = 300  # How often to check batch status

    # Failure tracking configuration
    failure_window_minutes: int = 60  # Time window for tracking failures
    max_consecutive_failures: int = 5  # Max failures before extended backoff
    # Additional multiplier for extended backoff
    extended_backoff_multiplier: float = 5.0


@dataclass
class DataSourceConfig:
    """Configuration for data source filtering."""
    start_date: Optional[str] = None
    end_date: Optional[str] = None
    filter_target_dates: bool = False
    target_date_threshold: float = 0.01
    target_date_interval: str = "1d"
    target_date_days_before: int = 3
    target_date_days_after: int = 3
    min_words: int = 200


def get_target_dates(
    start_date: datetime,
    end_date: datetime,
    threshold: float = 0.01,
    interval: str = "1d",
    days_before: int = 3,
    days_after: int = 3,
) -> Set[date]:
    """
    Fetch dates where SPY's absolute daily return exceeded the given threshold.

    Args:
        start_date: Start date for analysis.
        end_date: End date for analysis.
        threshold: Minimum absolute daily return (e.g., 0.01 for 1%).
        interval: Data interval, default is "1d".
        days_before: How many days before the target date to include.
        days_after: How many days after the target date to include.

    Returns:
        Set of target dates (datetime.date).
    """
    try:
        df = yahoo_api.get_price_data(
            ticker="SPY",
            start_date=start_date,
            end_date=end_date,
            interval=interval
        )
    except Exception as e:
        logger.error(f"Error fetching price data: {e}")
        return set()

    if df.empty or "Close" not in df.columns:
        logger.warning("No price data available for the specified period")
        return set()

    df["return"] = df["Close"].pct_change()
    mask = df["return"].abs() > threshold
    filtered = df[mask]

    result = set()
    for ts in filtered.index:
        base_date = ts.replace(tzinfo=TZ).date()
        for offset in range(-days_before, days_after + 1):
            result.add(base_date + timedelta(days=offset))

    return result


@dataclass
class APIFailureState:
    """Tracks failure state for an API."""
    current_backoff_delay: float = 0.0
    consecutive_failures: int = 0
    last_failure_time: Optional[datetime] = None
    next_retry_time: Optional[datetime] = None
    total_failures: int = 0
    failure_history: List[datetime] = None

    def __post_init__(self):
        if self.failure_history is None:
            self.failure_history = []


@dataclass
class APIStats:
    """Statistics for an API."""
    successful_batches: int = 0
    failed_batches: int = 0
    total_processing_time: float = 0.0
    articles_processed: int = 0
    average_batch_time: float = 0.0
    last_success_time: Optional[datetime] = None
    last_failure_time: Optional[datetime] = None


class BatchItem:
    """Represents a batch item in the processing queue."""

    def __init__(self, articles: List[Dict[str, Any]], prompt_type: str, batch_id: str,
                 priority: str = 'normal', preferred_api: Optional[str] = None):
        self.articles = articles
        self.prompt_type = prompt_type
        self.batch_id = batch_id
        self.created_at = datetime.now()
        self.retry_count = 0
        self.priority = priority  # 'high', 'normal', 'retry'
        self.preferred_api = preferred_api  # Preferred API for processing
        self.failed_apis: Set[str] = set()  # APIs that have failed this batch
        self.last_error: Optional[str] = None
        self.processing_start_time: Optional[datetime] = None

    def __len__(self):
        return len(self.articles)

    def mark_api_failed(self, api_name: str, error: str):
        """Mark an API as failed for this batch."""
        self.failed_apis.add(api_name)
        self.last_error = error
        self.retry_count += 1

    def can_use_api(self, api_name: str) -> bool:
        """Check if this batch can use the specified API."""
        return api_name not in self.failed_apis

    def get_available_apis(self, all_apis: List[str]) -> List[str]:
        """Get list of APIs that haven't failed for this batch."""
        return [api for api in all_apis if self.can_use_api(api)]


class LLMBatchAnalyzerV2:
    """
    Advanced async batch processor with queue-based architecture.

    Features:
    - Four concurrent async components
    - Queue-based batch distribution
    - API priority handling (OpenAI first, then Anthropic)
    - Exponential backoff with configurable parameters
    - Proper async context management
    - Enhanced error handling and recovery
    """

    def __init__(self, llm_configs: Dict[str, LLMConfig], async_config: AsyncBatchConfig,
                 api_configs: Optional[Dict[str, APIConfig]] = None,
                 data_source_config: Optional[DataSourceConfig] = None,
                 db: DatabaseManager = None):
        self.llm_configs = llm_configs
        self.async_config = async_config
        self.db = db
        self.prompt_manager = PromptManager()
        self.llm_apis = self._initialize_llm_apis()

        # Initialize per-API configurations
        self.api_configs = api_configs or self._create_default_api_configs()
        self.data_source_config = data_source_config or DataSourceConfig()

        # Multi-queue priority system
        self.high_priority_queue: asyncio.Queue = asyncio.Queue(
            maxsize=async_config.high_priority_queue_maxsize)
        self.low_priority_queue: asyncio.Queue = asyncio.Queue(
            maxsize=async_config.low_priority_queue_maxsize)
        self.retry_queue: asyncio.Queue = asyncio.Queue(
            maxsize=async_config.retry_queue_maxsize)

        # Async coordination
        self.running = True
        self.shutdown_event = asyncio.Event()

        # Per-API state tracking
        self.active_batches: Dict[str, Set[str]] = {
            api: set() for api in self.llm_apis.keys()}
        self.api_failures: Dict[str, APIFailureState] = {
            api: APIFailureState() for api in self.llm_apis.keys()}
        self.api_stats: Dict[str, APIStats] = {
            api: APIStats() for api in self.llm_apis.keys()}

        # Global statistics
        self.global_stats = {
            'batches_created': 0,
            'batches_completed': 0,
            'batches_failed': 0,
            'articles_processed': 0,
            'high_priority_batches': 0,
            'low_priority_batches': 0,
            'retry_batches': 0
        }

    def _initialize_llm_apis(self) -> Dict[str, BaseAPIManager]:
        """Initialize the LLM APIs."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager
        }

        llm_apis = {}
        for api_name, api_class in api_mapping.items():
            llm_apis[api_name] = api_class(
                requests_per_minute=self.llm_configs[api_name].requests_per_minute
            )
        return llm_apis

    def _create_default_api_configs(self) -> Dict[str, APIConfig]:
        """Create default API configurations."""
        return {
            'openai': APIConfig(
                max_concurrent_batches=50,
                max_retries=2,
                initial_backoff_delay=1.0,
                max_backoff_delay=300.0,
                backoff_multiplier=2.0,
                priority_level=1,  # Highest priority
                api_request_timeout=30.0,
                batch_creation_timeout=120.0,
                can_access_high_priority=True,
                can_access_low_priority=True,
                can_access_retry_queue=True
            ),
            'anthropic': APIConfig(
                max_concurrent_batches=50,
                max_retries=2,
                initial_backoff_delay=1.0,
                max_backoff_delay=300.0,
                backoff_multiplier=2.0,
                priority_level=2,  # Lower priority
                api_request_timeout=30.0,
                batch_creation_timeout=120.0,
                can_access_high_priority=False,  # Cannot access high priority queue
                can_access_low_priority=True,
                can_access_retry_queue=True
            )
        }

    async def calculate_api_backoff_delay(self, api_name: str) -> float:
        """Calculate per-API exponential backoff delay."""
        failure_state = self.api_failures[api_name]
        api_config = self.api_configs[api_name]

        if failure_state.consecutive_failures == 0:
            return 0.0

        # Calculate base exponential backoff
        delay = api_config.initial_backoff_delay * (
            api_config.backoff_multiplier ** (
                failure_state.consecutive_failures - 1)
        )

        # Apply extended backoff if too many consecutive failures
        if failure_state.consecutive_failures >= self.async_config.max_consecutive_failures:
            delay *= self.async_config.extended_backoff_multiplier

        return min(delay, api_config.max_backoff_delay)

    def is_api_available(self, api_name: str) -> bool:
        """Check if an API is available based on failure state, backoff timers, and concurrent batch limits."""
        if api_name not in self.api_configs:
            return False

        api_config = self.api_configs[api_name]
        failure_state = self.api_failures[api_name]

        # Check concurrent batch limit
        if len(self.active_batches[api_name]) >= api_config.max_concurrent_batches:
            return False

        # Check if we're in backoff period
        if failure_state.next_retry_time and datetime.now() < failure_state.next_retry_time:
            return False

        return True

    async def handle_api_failure(self, api_name: str, error: Exception, batch_item: BatchItem) -> None:
        """Manage failure tracking, backoff calculation, and retry queue placement."""
        failure_state = self.api_failures[api_name]
        api_stats = self.api_stats[api_name]

        # Update failure tracking
        failure_state.consecutive_failures += 1
        failure_state.total_failures += 1
        failure_state.last_failure_time = datetime.now()
        failure_state.failure_history.append(datetime.now())

        # Clean old failure history (keep only recent failures)
        cutoff_time = datetime.now() - timedelta(minutes=self.async_config.failure_window_minutes)
        failure_state.failure_history = [
            t for t in failure_state.failure_history if t > cutoff_time
        ]

        # Calculate backoff delay and set next retry time
        backoff_delay = await self.calculate_api_backoff_delay(api_name)
        failure_state.current_backoff_delay = backoff_delay
        failure_state.next_retry_time = datetime.now() + timedelta(seconds=backoff_delay)

        # Update API stats
        api_stats.failed_batches += 1
        api_stats.last_failure_time = datetime.now()

        # Mark batch as failed for this API
        batch_item.mark_api_failed(api_name, str(error))

        # Put batch in retry queue if it can still be processed by other APIs
        available_apis = batch_item.get_available_apis(
            list(self.llm_apis.keys()))
        if available_apis:
            batch_item.priority = 'retry'
            try:
                await asyncio.wait_for(
                    self.retry_queue.put(batch_item),
                    timeout=5.0  # Short timeout for retry queue
                )
                self.global_stats['retry_batches'] += 1
                logger.info(f"API Failure Handler: Moved batch {batch_item.batch_id} to retry queue "
                            f"(available APIs: {available_apis})")
            except asyncio.TimeoutError:
                logger.error(
                    f"API Failure Handler: Retry queue full, dropping batch {batch_item.batch_id}")
                self.global_stats['batches_failed'] += 1
        else:
            logger.error(
                f"API Failure Handler: No available APIs for batch {batch_item.batch_id}, dropping")
            self.global_stats['batches_failed'] += 1

        logger.warning(f"API Failure Handler: {api_name} failed (consecutive: {failure_state.consecutive_failures}, "
                       f"backoff: {backoff_delay:.1f}s, next retry: {failure_state.next_retry_time})")

    def get_next_available_api(self) -> Optional[str]:
        """Return the best available API considering priority, load, and failure states."""
        available_apis = []

        for api_name in self.llm_apis.keys():
            if self.is_api_available(api_name):
                api_config = self.api_configs[api_name]
                current_load = len(self.active_batches[api_name])
                load_ratio = current_load / api_config.max_concurrent_batches

                available_apis.append({
                    'name': api_name,
                    'priority': api_config.priority_level,
                    'load_ratio': load_ratio,
                    'consecutive_failures': self.api_failures[api_name].consecutive_failures
                })

        if not available_apis:
            return None

        # Sort by priority (lower number = higher priority), then by load, then by failure count
        available_apis.sort(key=lambda x: (
            x['priority'], x['load_ratio'], x['consecutive_failures']))

        return available_apis[0]['name']

    async def route_batch_by_priority(self, batch_item: BatchItem) -> str:
        """Determine which queue to use based on batch characteristics and API availability."""
        # Retry batches always go to retry queue
        if batch_item.priority == 'retry':
            return 'retry'

        # High priority batches go to high priority queue
        if batch_item.priority == 'high':
            return 'high'

        # For normal priority, check API availability and load
        best_api = self.get_next_available_api()
        if not best_api:
            return 'low'  # All APIs busy, use low priority queue

        api_config = self.api_configs[best_api]
        current_load = len(self.active_batches[best_api])
        load_ratio = current_load / api_config.max_concurrent_batches

        # If best API has low load and high priority, use high priority queue
        if api_config.priority_level == 1 and load_ratio < 0.9:
            return 'high'
        else:
            return 'low'

    def get_article_candidates(self, prompt_type: str, limit: int = 1000) -> List[Dict[str, Any]]:
        """Get articles that need processing."""
        # Get date range
        config = self.data_source_config
        start_dt, end_dt = self.db.article_service.get_article_date_range()
        if config.start_date:
            start_dt = max(start_dt, datetime.strptime(
                config.start_date, "%Y-%m-%d").replace(tzinfo=TZ))
        if config.end_date:
            end_dt = min(end_dt, datetime.strptime(
                config.end_date, "%Y-%m-%d").replace(tzinfo=TZ))

        # Get target dates
        target_dates = get_target_dates(
            start_dt, end_dt, config.target_date_threshold,
            config.target_date_interval, config.target_date_days_before,
            config.target_date_days_after
        ) if config.filter_target_dates else None

        if target_dates:
            logger.info(f"Filter articles on {len(target_dates)} target dates")

        articles = self.db.article_service.get_articles_for_llm_batch(
            # Exclude already succeeded articles with the api and prompt type
            prompt_type,
            llm_excluded_statuses=[CompletionStatus.SUCCEEDED.value,
                                   CompletionStatus.IN_PROGRESS.value],
            date_list=target_dates,
            start_date=start_dt,
            end_date=end_dt,
            limit=limit,
            # Filter short articles
            min_words=config.min_words
        )
        if start_dt and end_dt:
            logger.info(
                f"Fetched {len(articles)} articles between {start_dt.strftime('%Y-%m-%d')} and {end_dt.strftime('%Y-%m-%d')}")
        else:
            logger.info(f"Fetched {len(articles)} articles")

        return articles

    def save_completion(self, result_dict: Dict[str, Any], api_name: str, prompt_type: str, batch_id: str) -> Optional[Dict[str, Any]]:
        """Save completion result to database."""
        try:
            article_id = extract_article_id(result_dict['custom_id'])
            db_record = {
                "id": result_dict['custom_id'],
                'article_id': article_id,
                "api": api_name,
                "prompt_type": prompt_type,
                "model": result_dict['model'],
                "status": result_dict['status'],
                "batch_id": batch_id,
                "cost": result_dict.get('cost') or result_dict.get('estimated_cost'),
                "raw_response": result_dict.get('raw_response')
            }
            if 'user_prompt' in result_dict and 'system_prompt' in result_dict:
                db_record['prompt'] = result_dict['user_prompt'] + \
                    result_dict['system_prompt']

            if 'content' in result_dict:
                parsed_result = self.prompt_manager.parse_result(
                    result_dict['content'], prompt_type)
                db_record['content'] = parsed_result

                # Save to article metadata
                if self.db.article_service.add_article_metadata(
                        article_id, {prompt_type: db_record['content']}):
                    logger.debug(
                        f"Successfully saved in article metadata: {article_id} ")
                else:
                    logger.error(
                        f"Failed to save in article metadata: {article_id}")

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.debug(
                    f"Successfully saved result: {result_dict['custom_id']}")
            else:
                logger.error(
                    f"Failed to save result: {result_dict['custom_id']}")

            return db_record
        except Exception as e:
            logger.error(f"Error saving result: {e}")

        return None

    def save_batch(self, batch_dict: Dict[str, Any], api_name: str, prompt_type: str) -> Dict[str, Any]:
        """Save batch request information to database."""
        try:
            db_record = {
                'id': batch_dict['id'],
                'api': api_name,
                'status': batch_dict['status'],
                'created_at': batch_dict['created_at'],
                'completed_at': batch_dict.get('completed_at'),
                'expires_at': batch_dict['expires_at'],
                'prompt_type': prompt_type,
                'raw_response': batch_dict['raw_response'],
                'cost': batch_dict.get('cost')
            }
            self.db.llm_api_service.upsert_llm_batch(db_record)

            logger.info(f"Successfully saved batch: {batch_dict['id']}")
            return db_record

        except Exception as e:
            logger.error(f"Error saving batch: {e}")
            return {}

    def stop_processing(self):
        """Stop all processing gracefully."""
        self.running = False
        self.shutdown_event.set()
        logger.info("Batch processing stop requested")

    # ============================================================================
    # COMPONENT 1: BATCH CREATOR
    # ============================================================================

    async def batch_creator_component(self, prompt_type: str, batch_size: int = 20) -> None:
        """
        Component 1: Batch Creator

        Continuously reads articles from database, groups them into batches,
        and puts them into the shared queue for processing.
        """
        logger.info(f"Starting Batch Creator component for {prompt_type}")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Get articles that need processing
                    articles = self.get_article_candidates(
                        prompt_type, limit=self.async_config.max_articles_per_cycle)

                    if articles:
                        logger.info(
                            f"Batch Creator: Found {len(articles)} articles to process")

                        # Split articles into batches using dynamic batch sizing
                        batch_count = 0
                        high_priority_count = 0
                        low_priority_count = 0

                        for i in range(0, len(articles), batch_size):
                            if not self.running or self.shutdown_event.is_set():
                                break

                            article_batch = articles[i:i + batch_size]
                            batch_id = f"batch_{prompt_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{i//batch_size}"

                            batch_item = BatchItem(
                                articles=article_batch,
                                prompt_type=prompt_type,
                                batch_id=batch_id,
                                priority='normal'  # Default priority
                            )

                            # Determine which queue to use
                            queue_type = await self.route_batch_by_priority(batch_item)

                            # Select the appropriate queue
                            if queue_type == 'high':
                                target_queue = self.high_priority_queue
                                high_priority_count += 1
                                self.global_stats['high_priority_batches'] += 1
                            elif queue_type == 'retry':
                                target_queue = self.retry_queue
                                self.global_stats['retry_batches'] += 1
                            else:  # 'low'
                                target_queue = self.low_priority_queue
                                low_priority_count += 1
                                self.global_stats['low_priority_batches'] += 1

                            # Put batch in appropriate queue (with timeout to avoid blocking)
                            try:
                                await asyncio.wait_for(
                                    target_queue.put(batch_item),
                                    timeout=10.0  # Reasonable timeout for queue operations
                                )
                                batch_count += 1
                                logger.debug(
                                    f"Batch Creator: Queued batch {batch_id} in {queue_type} queue "
                                    f"with {len(article_batch)} articles")
                                await asyncio.sleep(2)

                            except asyncio.TimeoutError:
                                logger.warning(
                                    f"Batch Creator: {queue_type} queue full, skipping batch {batch_id}")
                                continue

                        logger.info(
                            f"Batch Creator: Successfully queued {batch_count} batches "
                            f"(high: {high_priority_count}, low: {low_priority_count})")
                        self.global_stats['batches_created'] += batch_count

                    else:
                        logger.debug(
                            f"Batch Creator: No new articles found for {prompt_type}. Stopping batch creation...")
                        self.stop_processing()

                    # Wait before next cycle
                    await asyncio.sleep(self.async_config.batch_creation_interval)

                except Exception as e:
                    logger.error(
                        f"Batch Creator: Error in processing cycle: {e}")
                    await asyncio.sleep(self.async_config.batch_creation_interval)

        except asyncio.CancelledError:
            logger.info("Batch Creator: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"Batch Creator: Fatal error: {e}")
            raise
        finally:
            logger.info("Batch Creator: Component stopped")

    # ============================================================================
    # BATCH PROCESSOR COMPONENT
    # ============================================================================

    async def _create_batch_request(self, api_name: str, batch_item: BatchItem) -> Optional[Any]:
        """Create a batch request for the specified API with enhanced error handling."""
        api_config = self.api_configs[api_name]
        api_stats = self.api_stats[api_name]
        llm_api = self.llm_apis[api_name]
        llm_config = self.llm_configs[api_name]
        start_time = datetime.now()

        try:
            # Mark processing start time
            batch_item.processing_start_time = start_time
            requests = []
            prompt = self.prompt_manager.get_prompt(batch_item.prompt_type)
            system_prompt = prompt['system_prompt']
            model = llm_config.model

            for article in batch_item.articles:
                article_input = process_article(
                    article, llm_config.min_input, llm_config.max_input)
                if not article_input:
                    continue

                rid = generate_custom_id(
                    article['id'], batch_item.prompt_type, api_name)

                formatted_prompt = prompt['prompt_template'].format(
                    title=article_input['title'],
                    content=article_input['content']
                )

                request = CompletionRequest(
                    max_tokens=llm_config.max_tokens,
                    temperature=llm_config.temperature,
                    user_prompt=formatted_prompt,
                    system_prompt=system_prompt,
                    model=model,
                    custom_id=rid
                )
                requests.append(request)

            if not requests:
                logger.warning(
                    f"{api_name} Processor: No valid requests in batch {batch_item.batch_id}")
                return None

            # Create batch with API-specific timeout
            batch = await asyncio.wait_for(
                asyncio.to_thread(llm_api.get_completion_batch, requests),
                timeout=api_config.batch_creation_timeout
            )

            if not batch:
                return None

            logger.info(
                f"{api_name} Processor: Batch {batch.id} is validating...")
            validation_timeout = 60  # 1 minute timeout for validation
            validation_start = asyncio.get_event_loop().time()

            while (batch and batch.status == BatchStatus.VALIDATING.value and
                    (asyncio.get_event_loop().time() - validation_start) < validation_timeout):
                await asyncio.sleep(2)
                batch = await asyncio.to_thread(
                    llm_api.retrieve_batch, batch.id, fetch_results=False)

            if batch and batch.status == BatchStatus.FAILED.value:
                logger.error(
                    f"{api_name} Processor: Batch {batch.id} failed validation")
                return None

            # Save batch and requests to database
            if batch:
                self.save_batch(batch.to_dict(), api_name,
                                batch_item.prompt_type)
                self.active_batches[api_name].add(batch.id)

                for request in requests:
                    request_dict = request.to_dict()
                    request_dict['status'] = CompletionStatus.IN_PROGRESS.value
                    self.save_completion(
                        request_dict, api_name, batch_item.prompt_type, batch_id=batch.id)

                # Update success statistics
                processing_time = (datetime.now() - start_time).total_seconds()
                api_stats.successful_batches += 1
                api_stats.articles_processed += len(batch_item)
                api_stats.total_processing_time += processing_time
                api_stats.average_batch_time = api_stats.total_processing_time / \
                    api_stats.successful_batches
                api_stats.last_success_time = datetime.now()

                # Reset consecutive failures on success
                self.api_failures[api_name].consecutive_failures = 0
                self.api_failures[api_name].current_backoff_delay = 0.0
                self.api_failures[api_name].next_retry_time = None

            return batch

        except asyncio.TimeoutError:
            error_msg = f"Timeout creating batch {batch_item.batch_id}"
            logger.error(f"{api_name} Processor: {error_msg}")
            return None
        except Exception as e:
            error_msg = f"Error creating batch {batch_item.batch_id}: {e}"
            logger.error(f"{api_name} Processor: {error_msg}")
            return None

    async def batch_processor_component(self, api_name: str) -> None:
        """
        Batch Processor Component

        Processes batches from multiple priority queues based on API configuration.
        Implements per-API exponential backoff, failure tracking, and load balancing.
        """
        api_config = self.api_configs[api_name]
        logger.info(
            f"Starting {api_name.title()} Processor component (Priority: {api_config.priority_level})")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Check if API is available (not in backoff, under concurrent limit)
                    if not self.is_api_available(api_name):
                        next_retry_time = self.api_failures[api_name].next_retry_time
                        if next_retry_time:
                            delay = (next_retry_time -
                                     datetime.now()).total_seconds()
                            logger.info(
                                f"{api_name} Processor: Waiting for next retry time ({delay:.1f}s)")
                            await asyncio.sleep(delay)
                        continue

                    # Try to get batch from queues based on API permissions and priority
                    batch_item = None
                    queue_source = None

                    # Priority 1: Retry queue (all APIs can access)
                    if api_config.can_access_retry_queue and not self.retry_queue.empty():
                        try:
                            batch_item = await asyncio.wait_for(
                                self.retry_queue.get(),
                                timeout=0.5  # Quick check
                            )
                            queue_source = "retry"
                        except asyncio.TimeoutError:
                            pass

                    # Priority 2: High priority queue (if allowed)
                    if not batch_item and api_config.can_access_high_priority and not self.high_priority_queue.empty():
                        try:
                            batch_item = await asyncio.wait_for(
                                self.high_priority_queue.get(),
                                timeout=0.5  # Quick check
                            )
                            queue_source = "high_priority"
                        except asyncio.TimeoutError:
                            pass

                    # Priority 3: Low priority queue (if allowed)
                    if not batch_item and api_config.can_access_low_priority and not self.low_priority_queue.empty():
                        try:
                            batch_item = await asyncio.wait_for(
                                self.low_priority_queue.get(),
                                timeout=0.5  # Quick check
                            )
                            queue_source = "low_priority"
                        except asyncio.TimeoutError:
                            pass

                    # If no batch available, wait and continue, or exit
                    if not batch_item:
                        await asyncio.sleep(5)
                        continue

                    # Check if this batch can use this API
                    if not batch_item.can_use_api(api_name):
                        logger.debug(
                            f"{api_name} Processor: Batch {batch_item.batch_id} cannot use this API, skipping")
                        # Put batch back in appropriate queue
                        if queue_source == "retry":
                            await self.retry_queue.put(batch_item)
                        elif queue_source == "high_priority":
                            await self.high_priority_queue.put(batch_item)
                        else:
                            await self.low_priority_queue.put(batch_item)
                        continue

                    logger.info(f"{api_name} Processor: Processing batch {batch_item.batch_id} "
                                f"from {queue_source} queue with {len(batch_item)} articles")

                    # Attempt to create batch with API-specific retry logic
                    batch = None
                    for attempt in range(api_config.max_retries):
                        try:
                            batch = await self._create_batch_request(api_name, batch_item)
                            if batch:
                                logger.info(
                                    f"{api_name} Processor: Successfully created batch {batch.id}")
                                self.global_stats['articles_processed'] += len(
                                    batch_item)
                                break
                            else:
                                logger.warning(
                                    f"{api_name} Processor: Failed to create batch (attempt {attempt + 1})")

                        except Exception as e:
                            logger.error(
                                f"{api_name} Processor: Error on attempt {attempt + 1}: {e}")

                        # API-specific exponential backoff before retry
                        if attempt < api_config.max_retries - 1:
                            delay = await self.calculate_api_backoff_delay(api_name)
                            if delay > 0:
                                logger.info(
                                    f"{api_name} Processor: Retrying in {delay:.1f}s...")
                                await asyncio.sleep(delay)
                        else:
                            error_msg = f"{api_name} Processor: Failed to create batch after {api_config.max_retries} attempts"
                            logger.error(error_msg)
                            await self.handle_api_failure(api_name, Exception(error_msg), batch_item)

                    # Mark queue task as done
                    if queue_source == "retry":
                        self.retry_queue.task_done()
                    elif queue_source == "high_priority":
                        self.high_priority_queue.task_done()
                    else:
                        self.low_priority_queue.task_done()

                except Exception as e:
                    logger.error(
                        f"{api_name} Processor: Error processing batch: {e}")
                    await asyncio.sleep(60)

        except asyncio.CancelledError:
            logger.info(f"{api_name} Processor: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"{api_name} Processor: Fatal error: {e}")
            raise
        finally:
            logger.info(f"{api_name} Processor: Component stopped")

    # ============================================================================
    # COMPONENT 4: BATCH MONITOR
    # ============================================================================

    async def _process_single_batch(self, batch_record: Dict[str, Any], prompt_type: str) -> None:
        """Process a single batch and handle results."""
        try:
            api_name = batch_record['api']
            batch_id = batch_record['id']
            llm_api = self.llm_apis.get(api_name)

            if not llm_api:
                logger.error(f"Batch Monitor: Unknown API: {api_name}")
                return

            # Retrieve batch status and results
            batch = await asyncio.to_thread(
                llm_api.retrieve_batch, batch_id, fetch_results=True)
            if not batch:
                logger.error(
                    f"Batch Monitor: Failed to retrieve batch {batch_id}")
                return

            logger.debug(
                f"Batch Monitor: Retrieved batch {batch.id} with status {batch.status}")

            if batch.status in COMPLETED_BATCH_STATUSES:
                logger.info(
                    f"Batch Monitor: Batch {batch.id} completed. Processing {len(batch.completion_results)} results...")

                # Process completed results
                for result in batch.completion_results:
                    self.save_completion(
                        result.to_dict(), api_name, prompt_type, batch_id=batch.id)

                # Mark batch as processed
                batch.status = BatchStatus.PROCESSED.value
                self.global_stats['batches_completed'] += 1
                self.api_stats[api_name].successful_batches += 1

                # Remove from active tracking
                self.active_batches[api_name].discard(batch_id)

            elif batch.status in INCOMPLETED_BATCH_STATUSES:
                logger.error(
                    f"Batch Monitor: Batch {batch.id} failed with status {batch.status}")

                # Mark all requests as failed
                requests = self.db.llm_api_service.get_llm_results(
                    batch_id=batch.id)
                logger.info(
                    f"Batch Monitor: Marking {len(requests)} requests as failed...")
                for request in requests:
                    request['status'] = CompletionStatus.FAILED.value
                    self.save_completion(
                        request, api_name, prompt_type, batch_id=batch.id)

                self.global_stats['batches_failed'] += 1
                self.api_stats[api_name].failed_batches += 1

                # Remove from active tracking
                self.active_batches[api_name].discard(batch_id)

            elif batch.status in ACTIVE_BATCH_STATUSES:
                logger.debug(
                    f"Batch Monitor: Batch {batch.id} is still active")
            else:
                logger.warning(
                    f"Batch Monitor: Unknown batch status {batch.status} for batch {batch.id}")

            # Update batch status in database
            self.save_batch(batch.to_dict(), api_name, prompt_type)

        except Exception as e:
            logger.error(
                f"Batch Monitor: Error processing batch {batch_record.get('id', 'unknown')}: {e}")

    async def batch_monitor_component(self, prompt_type: str) -> None:
        """
        Component 4: Batch Monitor

        Continuously polls all active batch requests across both APIs,
        checks batch status, and processes completed results.
        """
        logger.info(f"Starting Batch Monitor component for {prompt_type}")

        try:
            while self.running and not self.shutdown_event.is_set():
                try:
                    # Get active batches from database
                    active_batches = self.db.llm_api_service.get_llm_batch_status(
                        prompt_type=prompt_type,
                        included_status=ACTIVE_BATCH_STATUSES
                    )

                    if active_batches:
                        logger.info(
                            f"Batch Monitor: Found {len(active_batches)} active batches to check")

                        # Process batches concurrently but with limited concurrency
                        # Limit concurrent batch processing
                        semaphore = asyncio.Semaphore(5)

                        async def process_with_semaphore(batch_record):
                            async with semaphore:
                                await self._process_single_batch(batch_record, prompt_type)
                                # Small delay between batch processing
                                await asyncio.sleep(0.5)

                        # Process all active batches concurrently
                        await asyncio.gather(
                            *[process_with_semaphore(batch_record)
                              for batch_record in active_batches],
                            return_exceptions=True
                        )

                    else:
                        logger.debug(
                            f"Batch Monitor: No active batches found for {prompt_type}")

                    # Wait for next polling cycle
                    logger.debug(
                        f"Batch Monitor: Sleeping for {self.async_config.batch_monitoring_interval}s...")
                    await asyncio.sleep(self.async_config.batch_monitoring_interval)

                except Exception as e:
                    logger.error(
                        f"Batch Monitor: Error in monitoring cycle: {e}")
                    # Continue monitoring even if there's an error, but wait a bit longer
                    await asyncio.sleep(min(self.async_config.batch_monitoring_interval, 60))

        except asyncio.CancelledError:
            logger.info("Batch Monitor: Component cancelled")
            raise
        except Exception as e:
            logger.error(f"Batch Monitor: Fatal error: {e}")
            raise
        finally:
            logger.info("Batch Monitor: Component stopped")

    # ============================================================================
    # MAIN ORCHESTRATION METHODS
    # ============================================================================

    async def run_async_batch_processing(
        self,
        prompt_type: str,
        batch_size: int = 20
    ) -> None:
        """
        Main method to run all four async components concurrently.

        This orchestrates the entire async batch processing pipeline:
        1. Batch Creator - reads articles and creates batches
        2. OpenAI Processor - processes batches with high priority
        3. Anthropic Processor - processes remaining batches
        4. Batch Monitor - monitors and processes completed batches
        """
        logger.info(
            f"Starting async batch processing pipeline for {prompt_type}")
        logger.info(f"Configuration: {self.async_config}")

        try:
            # Create all four async tasks
            tasks = [
                asyncio.create_task(
                    self.batch_creator_component(
                        prompt_type=prompt_type,
                        batch_size=batch_size
                    ),
                    name="BatchCreator"
                ),
                asyncio.create_task(
                    self.batch_processor_component('openai'),
                    name="OpenAIProcessor"
                ),
                asyncio.create_task(
                    self.batch_processor_component('anthropic'),
                    name="AnthropicProcessor"
                ),
                asyncio.create_task(
                    self.batch_monitor_component(prompt_type),
                    name="BatchMonitor"
                )
            ]

            logger.info("All components started, running concurrently...")

            # Run all components concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)

            # Check for exceptions in any component
            for i, result in enumerate(results):
                task_name = tasks[i].get_name()
                if isinstance(result, Exception):
                    logger.error(f"Exception in {task_name}: {result}")
                else:
                    logger.info(f"{task_name} completed successfully")

        except asyncio.CancelledError:
            logger.info("Async batch processing cancelled")
            # Cancel all tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
            # Wait for all tasks to complete cancellation
            await asyncio.gather(*tasks, return_exceptions=True)
            raise
        except Exception as e:
            logger.error(f"Fatal error in async batch processing: {e}")
            # Cancel all tasks
            for task in tasks:
                if not task.done():
                    task.cancel()
            raise
        finally:
            # Print final statistics
            logger.info("=== Final Processing Statistics ===")
            logger.info("Global Statistics:")
            for key, value in self.global_stats.items():
                logger.info(f"  {key}: {value}")

            logger.info("Per-API Statistics:")
            for api_name, stats in self.api_stats.items():
                logger.info(f"  {api_name}:")
                logger.info(
                    f"    Successful batches: {stats.successful_batches}")
                logger.info(f"    Failed batches: {stats.failed_batches}")
                logger.info(
                    f"    Articles processed: {stats.articles_processed}")
                logger.info(
                    f"    Average batch time: {stats.average_batch_time:.2f}s")

            logger.info("Per-API Failure States:")
            for api_name, failure_state in self.api_failures.items():
                logger.info(f"  {api_name}:")
                logger.info(
                    f"    Consecutive failures: {failure_state.consecutive_failures}")
                logger.info(
                    f"    Total failures: {failure_state.total_failures}")
                logger.info(
                    f"    Current backoff: {failure_state.current_backoff_delay:.1f}s")

            logger.info("Async batch processing pipeline stopped")

    @asynccontextmanager
    async def async_context(self):
        """Async context manager for proper resource cleanup."""
        try:
            logger.info("LLMBatchAnalyzerV2: Async context started")
            yield self
        finally:
            # Cleanup resources
            self.stop_processing()

            # Wait for all queues to be empty
            queues_to_wait = [
                ("high_priority", self.high_priority_queue),
                ("low_priority", self.low_priority_queue),
                ("retry", self.retry_queue)
            ]

            for queue_name, queue in queues_to_wait:
                if not queue.empty():
                    logger.info(f"Waiting for {queue_name} queue to empty...")
                    await queue.join()

            logger.info("LLMBatchAnalyzerV2: Resources cleaned up")


# ============================================================================
# COMMAND LINE INTERFACE
# ============================================================================

async def main():
    """Main function to run the v2 analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='LLM Batch Analyzer V2 - Advanced async batch processing')

    parser.add_argument('-t', '--prompt-type',
                        default='influence', help='Type of analysis to perform')

    # Date filtering
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')
    parser.add_argument('--filter-target-dates', action='store_true',
                        help='Filter dates based on SPY price movements')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=0.1,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-input', type=int, default=2048,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=512,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=200,
                        help='Minimum input words')
    parser.add_argument('--requests-per-minute', type=int, default=10,
                        help='Rate limit per minute')

    # Batch processing configuration
    parser.add_argument('--max-articles-per-cycle', type=int, default=1000,
                        help='Maximum articles to process per cycle')
    parser.add_argument('--batch-size', type=int, default=20,
                        help='Batch size for processing')
    parser.add_argument('--queue-maxsize', type=int, default=100,
                        help='Maximum queue size for batches')

    # Timing configuration
    parser.add_argument('--batch-creation-interval', type=int, default=60,
                        help='Interval between batch creation cycles (seconds)')
    parser.add_argument('--batch-monitoring-interval', type=int, default=300,
                        help='Interval between batch monitoring cycles (seconds)')

    # Retry and backoff configuration
    parser.add_argument('--max-retries', type=int, default=3,
                        help='Maximum retries for failed operations')
    parser.add_argument('--initial-backoff-delay', type=float, default=1.0,
                        help='Initial backoff delay in seconds')
    parser.add_argument('--max-backoff-delay', type=float, default=300.0,
                        help='Maximum backoff delay in seconds')
    parser.add_argument('--backoff-multiplier', type=float, default=2.0,
                        help='Exponential backoff multiplier')

    # Timeout configuration
    parser.add_argument('--api-request-timeout', type=float, default=30.0,
                        help='Timeout for individual API requests (seconds)')
    parser.add_argument('--batch-creation-timeout', type=float, default=120.0,
                        help='Timeout for batch creation (seconds)')

    # Resource limits
    parser.add_argument('--max-concurrent-batches', type=int, default=50,
                        help='Maximum concurrent batches per API')

    args = parser.parse_args()

    try:
        # Create configurations
        llm_configs = {
            'openai': LLMConfig(
                model='gpt-4.1-nano',
                max_tokens=args.max_tokens,
                max_input=args.max_input,
                min_input=args.min_input,
                temperature=0.7,
                requests_per_minute=args.requests_per_minute
            ),
            'anthropic': LLMConfig(
                model='claude-3-5-haiku',
                max_tokens=args.max_tokens,
                max_input=args.max_input,
                min_input=args.min_input,
                temperature=0.7,
                requests_per_minute=args.requests_per_minute
            )
        }

        async_config = AsyncBatchConfig(
            max_articles_per_cycle=args.max_articles_per_cycle,
            high_priority_queue_maxsize=args.queue_maxsize // 2,
            low_priority_queue_maxsize=args.queue_maxsize,
            retry_queue_maxsize=args.queue_maxsize // 3,
            batch_creation_interval=args.batch_creation_interval,
            batch_monitoring_interval=args.batch_monitoring_interval
        )

        # Create per-API configurations from command line args
        api_configs = {
            'openai': APIConfig(
                max_concurrent_batches=args.max_concurrent_batches,
                max_retries=args.max_retries,
                initial_backoff_delay=args.initial_backoff_delay,
                max_backoff_delay=args.max_backoff_delay,
                backoff_multiplier=args.backoff_multiplier,
                priority_level=1,
                api_request_timeout=args.api_request_timeout,
                batch_creation_timeout=args.batch_creation_timeout,
                can_access_high_priority=True,
                can_access_low_priority=True,
                can_access_retry_queue=True
            ),
            'anthropic': APIConfig(
                max_concurrent_batches=args.max_concurrent_batches,
                max_retries=args.max_retries,
                initial_backoff_delay=args.initial_backoff_delay,
                max_backoff_delay=args.max_backoff_delay,
                backoff_multiplier=args.backoff_multiplier,
                priority_level=2,
                api_request_timeout=args.api_request_timeout,
                batch_creation_timeout=args.batch_creation_timeout,
                can_access_high_priority=False,  # Lower priority
                can_access_low_priority=True,
                can_access_retry_queue=True
            )
        }

        data_source_config = DataSourceConfig(
            start_date=args.start_date,
            end_date=args.end_date,
            filter_target_dates=args.filter_target_dates,
            min_words=args.min_input
        )

        # Initialize database and analyzer
        db_manager = DatabaseManager()
        batch_analyzer = LLMBatchAnalyzerV2(
            llm_configs, async_config, api_configs, data_source_config, db_manager)

        # Check budget
        current_total_cost = db_manager.llm_api_service.get_total_cost(
            prompt_type=args.prompt_type)
        if current_total_cost >= args.budget:
            logger.warning(f"Current cost {current_total_cost} exceeds budget limit "
                           f"{args.budget} for prompt type {args.prompt_type}.")
        BaseAPIManager.set_budget(args.budget, current_total_cost)

        # Run the async batch processing pipeline
        async with batch_analyzer.async_context():
            try:
                logger.info("=== LLM Batch Analyzer V2 Starting ===")
                logger.info(f"Prompt type: {args.prompt_type}")
                logger.info(f"Budget: ${args.budget}")
                logger.info(f"LLM Configs: {llm_configs}")
                logger.info(f"Async Config: {async_config}")
                logger.info("Starting async batch processing pipeline...")

                await batch_analyzer.run_async_batch_processing(
                    prompt_type=args.prompt_type,
                    batch_size=args.batch_size,
                )

            except KeyboardInterrupt:
                logger.info(
                    "Received interrupt signal, stopping processing...")
                batch_analyzer.stop_processing()
            except Exception as e:
                logger.error(f"Unexpected error during batch processing: {e}")
                batch_analyzer.stop_processing()
                raise

    except Exception as e:
        logger.error(f"Application error: {e}")
        raise


if __name__ == '__main__':
    asyncio.run(main())
